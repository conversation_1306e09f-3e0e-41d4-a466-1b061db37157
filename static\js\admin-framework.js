/**
 * 后台管理框架 - AdminFramework
 * 提供单页面应用的页面管理、路由、事件处理等功能
 *
 * 设计原则：
 * 1. 兼容现有页面的JavaScript逻辑
 * 2. 提供可靠的页面加载和事件绑定机制
 * 3. 支持动态页面加载和卸载
 * 4. 确保事件监听器的正确绑定和清理
 */

(function(window) {
    'use strict';

    // AdminFramework 主对象
    const AdminFramework = {
        // 当前加载的页面信息
        currentPage: null,
        currentRoute: null,

        // 页面缓存
        pageCache: new Map(),

        // 页面脚本缓存
        scriptCache: new Map(),

        // 已加载的页面脚本
        loadedScripts: new Set(),

        // 页面清理函数缓存
        cleanupFunctions: new Map(),

        /**
         * 初始化框架
         */
        init: function() {
            console.log('AdminFramework 初始化开始');

            // 隐藏加载遮罩
            this.hideLoadingOverlay();

            // 绑定导航事件
            this.bindNavigationEvents();

            // 初始化路由
            this.initializeRouting();

            // 设置全局错误处理
            this.setupErrorHandling();

            console.log('AdminFramework 初始化完成');
        },

        /**
         * 隐藏加载遮罩
         */
        hideLoadingOverlay: function() {
            setTimeout(() => {
                const loadingOverlay = document.getElementById('afLoadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.classList.add('af-hidden');
                    setTimeout(() => {
                        loadingOverlay.style.display = 'none';
                    }, 300);
                }
            }, 500);
        },

        /**
         * 绑定导航事件
         */
        bindNavigationEvents: function() {
            const navList = document.querySelector('.af-nav-list');
            if (!navList) return;

            navList.addEventListener('click', (e) => {
                const navItem = e.target.closest('.af-nav-item');
                if (!navItem) return;

                e.preventDefault();

                const route = navItem.getAttribute('data-af-route');
                if (route) {
                    this.navigateTo(route);
                }
            });
        },

        /**
         * 初始化路由
         */
        initializeRouting: function() {
            // 检查URL哈希值
            const hash = window.location.hash.replace('#', '');
            const defaultRoute = hash || 'home';

            // 添加popstate事件监听
            window.addEventListener('popstate', (event) => {
                if (event.state?.route) {
                    this.loadPage(event.state.route);
                } else {
                    this.loadPage(defaultRoute);
                }
            });

            // 加载默认页面
            this.navigateTo(defaultRoute);
        },

        /**
         * 导航到指定路由
         */
        navigateTo: function(route) {
            if (route === this.currentRoute) return;

            console.log(`导航到路由: ${route}`);

            // 更新URL
            window.history.pushState({ route }, '', `#${route}`);

            // 加载页面
            this.loadPage(route);

            // 更新导航状态
            this.updateNavigationState(route);
        },

        /**
         * 加载页面
         */
        loadPage: function(route) {
            console.log(`开始加载页面: ${route}`);

            // 显示进度条
            this.showProgressBar();

            // 清理当前页面
            this.cleanupCurrentPage();

            // 发送AJAX请求加载页面内容
            fetch(`/framework/page/${route}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(html => {
                // 更新页面内容
                this.updatePageContent(html, route);

                // 初始化页面脚本
                this.initializePageScripts(route);

                // 更新当前页面信息
                this.currentPage = html;
                this.currentRoute = route;

                console.log(`页面加载完成: ${route}`);
            })
            .catch(error => {
                console.error(`页面加载失败: ${route}`, error);
                this.showErrorPage(route, error);
            })
            .finally(() => {
                // 隐藏进度条
                this.hideProgressBar();
            });
        },

        /**
         * 更新页面内容
         */
        updatePageContent: function(html, route) {
            const pageContent = document.getElementById('afPageContent');
            if (pageContent) {
                // 过滤掉可能引用index.js的script标签
                let filteredHtml = html;

                // 移除对index.js的引用
                filteredHtml = filteredHtml.replace(/<script[^>]*src[^>]*index\.js[^>]*><\/script>/gi, '');
                filteredHtml = filteredHtml.replace(/<script[^>]*src[^>]*\/static\/js\/index\.js[^>]*><\/script>/gi, '');

                console.log(`AdminFramework: 更新页面内容 (${route})`);
                pageContent.innerHTML = filteredHtml;

                // 缓存页面内容
                this.pageCache.set(route, filteredHtml);
            }
        },

        /**
         * 初始化页面脚本
         */
        initializePageScripts: function(route) {
            console.log(`初始化页面脚本: ${route}`);

            // 等待DOM更新完成
            setTimeout(() => {
                // 根据路由执行相应的初始化逻辑
                switch(route) {
                    case 'category':
                        this.initializeCategoryPage();
                        break;
                    case 'home':
                        this.initializeHomePage();
                        break;
                    case 'productlist':
                        this.initializeProductListPage();
                        break;
                    // 可以添加更多页面的初始化逻辑
                    default:
                        this.initializeGenericPage(route);
                        break;
                }
            }, 100);
        },

        /**
         * 初始化分类页面
         */
        initializeCategoryPage: function() {
            console.log('初始化分类页面');

            // 确保category.js中的函数可用
            if (typeof window.initializeCategoryPageForFramework === 'function') {
                window.initializeCategoryPageForFramework();
            } else {
                // 如果函数不存在，使用定时器等待
                let attempts = 0;
                const maxAttempts = 50; // 最多等待5秒

                const waitForCategoryScript = () => {
                    attempts++;
                    if (typeof window.initializeCategoryPageForFramework === 'function') {
                        window.initializeCategoryPageForFramework();
                    } else if (attempts < maxAttempts) {
                        setTimeout(waitForCategoryScript, 100);
                    } else {
                        console.warn('分类页面初始化函数未找到，尝试直接初始化');
                        this.fallbackCategoryInitialization();
                    }
                };

                waitForCategoryScript();
            }
        },

        /**
         * 分类页面备用初始化方法
         */
        fallbackCategoryInitialization: function() {
            // 检查必要的DOM元素是否存在
            const categoryContainer = document.getElementById('category-container');
            if (!categoryContainer) {
                console.warn('分类页面容器未找到');
                return;
            }

            // 尝试调用全局的分类初始化函数
            if (typeof window.fetchCategories === 'function') {
                window.fetchCategories();
            }

            if (typeof window.initializeEventListeners === 'function') {
                window.initializeEventListeners();
            }
        },

        /**
         * 初始化首页
         */
        initializeHomePage: function() {
            console.log('初始化首页');
            // 首页初始化逻辑
        },

        /**
         * 初始化商品列表页面
         */
        initializeProductListPage: function() {
            console.log('初始化商品列表页面');
            // 商品列表页面初始化逻辑
        },

        /**
         * 通用页面初始化
         */
        initializeGenericPage: function(route) {
            console.log(`初始化通用页面: ${route}`);

            // 特殊页面处理 - PriceTemplate页面需要动态加载脚本
            if (route === 'PriceTemplate') {
                this.initializePriceTemplatePage();
            }
            // 可以在这里添加其他页面的特殊处理
        },

        /**
         * 初始化PriceTemplate页面 - 动态加载脚本
         */
        initializePriceTemplatePage: function() {
            console.log('AdminFramework: 开始初始化PriceTemplate页面');

            // 检查脚本是否已经加载或正在加载
            if (this.loadedScripts.has('PriceTemplate.js')) {
                console.log('AdminFramework: PriceTemplate.js已加载，直接调用初始化');
                this.callPriceTemplateInitialization();
                return;
            }

            // 检查是否已经存在该脚本标签
            const existingScript = document.querySelector('script[src="/static/js/pages/PriceTemplate.js"]');
            if (existingScript) {
                console.log('AdminFramework: PriceTemplate.js脚本标签已存在，等待加载完成');
                this.loadedScripts.add('PriceTemplate.js');

                // 延迟调用初始化
                setTimeout(() => {
                    this.callPriceTemplateInitialization();
                }, 200);
                return;
            }

            // 标记正在加载，防止重复加载
            this.loadedScripts.add('PriceTemplate.js');

            // 动态加载PriceTemplate.js脚本
            const script = document.createElement('script');
            script.src = '/static/js/pages/PriceTemplate.js';
            script.type = 'text/javascript';

            script.onload = () => {
                console.log('AdminFramework: PriceTemplate.js加载成功');

                // 延迟调用初始化，确保脚本完全执行
                setTimeout(() => {
                    this.callPriceTemplateInitialization();
                }, 100);
            };

            script.onerror = (error) => {
                console.error('AdminFramework: PriceTemplate.js加载失败', error);
                // 移除加载标记
                this.loadedScripts.delete('PriceTemplate.js');

                // 尝试备用初始化方法
                setTimeout(() => {
                    this.fallbackPriceTemplateInitialization();
                }, 200);
            };

            // 添加到head中
            document.head.appendChild(script);
        },

        /**
         * 调用PriceTemplate初始化函数
         */
        callPriceTemplateInitialization: function() {
            // 检查初始化函数是否可用
            if (typeof window.initializePriceTemplateForFramework === 'function') {
                console.log('AdminFramework: 调用PriceTemplate框架初始化函数');
                window.initializePriceTemplateForFramework();
            } else {
                console.warn('AdminFramework: PriceTemplate初始化函数未找到，尝试等待');

                // 等待函数可用
                let attempts = 0;
                const maxAttempts = 20; // 最多等待2秒

                const waitForFunction = () => {
                    attempts++;
                    if (typeof window.initializePriceTemplateForFramework === 'function') {
                        console.log('AdminFramework: 等待后找到PriceTemplate初始化函数');
                        window.initializePriceTemplateForFramework();
                    } else if (attempts < maxAttempts) {
                        setTimeout(waitForFunction, 100);
                    } else {
                        console.error('AdminFramework: 等待超时，尝试备用初始化');
                        this.fallbackPriceTemplateInitialization();
                    }
                };

                waitForFunction();
            }
        },

        /**
         * PriceTemplate页面备用初始化方法
         */
        fallbackPriceTemplateInitialization: function() {
            console.warn('AdminFramework: 使用PriceTemplate备用初始化方法');

            // 检查关键DOM元素
            const container = document.getElementById('pricing-template-container');
            if (!container) {
                console.error('AdminFramework: PriceTemplate页面容器未找到');
                return;
            }

            // 尝试调用可能存在的其他初始化函数
            if (typeof window.initializePage === 'function') {
                console.log('AdminFramework: 调用通用initializePage函数');
                window.initializePage();
            } else {
                console.error('AdminFramework: 无法找到任何PriceTemplate初始化函数');
            }
        },

        /**
         * 清理当前页面
         */
        cleanupCurrentPage: function() {
            if (!this.currentRoute) return;

            console.log(`清理页面: ${this.currentRoute}`);

            // 执行页面特定的清理函数
            const cleanupFn = this.cleanupFunctions.get(this.currentRoute);
            if (cleanupFn && typeof cleanupFn === 'function') {
                try {
                    cleanupFn();
                } catch (error) {
                    console.error(`页面清理失败: ${this.currentRoute}`, error);
                }
            }

            // 清理事件监听器
            this.cleanupEventListeners();
        },

        /**
         * 清理事件监听器
         */
        cleanupEventListeners: function() {
            // 移除可能的全局事件监听器
            // 这里可以根据需要添加具体的清理逻辑
        },

        /**
         * 更新导航状态
         */
        updateNavigationState: function(route) {
            // 移除所有活动状态
            document.querySelectorAll('.af-nav-item').forEach(item => {
                item.classList.remove('af-active');
            });

            // 添加当前路由的活动状态
            const currentNavItem = document.querySelector(`[data-af-route="${route}"]`);
            if (currentNavItem) {
                currentNavItem.classList.add('af-active');
            }
        },

        /**
         * 显示进度条
         */
        showProgressBar: function() {
            const progressBar = document.getElementById('afProgressBar');
            if (progressBar) {
                progressBar.style.width = '0%';
                progressBar.style.opacity = '1';

                // 模拟进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    progressBar.style.width = progress + '%';

                    if (progress >= 90) {
                        clearInterval(interval);
                    }
                }, 100);

                // 存储interval以便后续清理
                this._progressInterval = interval;
            }
        },

        /**
         * 隐藏进度条
         */
        hideProgressBar: function() {
            const progressBar = document.getElementById('afProgressBar');
            if (progressBar) {
                progressBar.style.width = '100%';
                setTimeout(() => {
                    progressBar.style.opacity = '0';
                    setTimeout(() => {
                        progressBar.style.width = '0%';
                    }, 300);
                }, 100);
            }

            // 清理进度条interval
            if (this._progressInterval) {
                clearInterval(this._progressInterval);
                this._progressInterval = null;
            }
        },

        /**
         * 显示错误页面
         */
        showErrorPage: function(route, error) {
            const pageContent = document.getElementById('afPageContent');
            if (pageContent) {
                pageContent.innerHTML = `
                    <div class="af-error-page" style="padding: 40px; text-align: center; color: #666;">
                        <h2 style="color: #e74c3c;">页面加载失败</h2>
                        <p>路由: <code>${route}</code></p>
                        <p>错误信息: ${error.message}</p>
                        <button onclick="AdminFramework.navigateTo('home')"
                                style="padding: 10px 20px; background: #ff6b9d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            返回控制台
                        </button>
                        <button onclick="AdminFramework.navigateTo('${route}')"
                                style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                            重试
                        </button>
                    </div>
                `;
            }
        },

        /**
         * 设置全局错误处理
         */
        setupErrorHandling: function() {
            window.addEventListener('error', (event) => {
                console.error('全局错误:', event.error);
            });

            window.addEventListener('unhandledrejection', (event) => {
                console.error('未处理的Promise拒绝:', event.reason);
            });
        },

        /**
         * 注册页面清理函数
         */
        registerCleanupFunction: function(route, cleanupFn) {
            this.cleanupFunctions.set(route, cleanupFn);
        },

        /**
         * 获取当前路由
         */
        getCurrentRoute: function() {
            return this.currentRoute;
        },

        /**
         * 刷新当前页面
         */
        refresh: function() {
            if (this.currentRoute) {
                this.loadPage(this.currentRoute);
            }
        }
    };

    // 暴露到全局
    window.AdminFramework = AdminFramework;

    // 页面加载完成后初始化框架
    document.addEventListener('DOMContentLoaded', function() {
        AdminFramework.init();
    });

})(window);